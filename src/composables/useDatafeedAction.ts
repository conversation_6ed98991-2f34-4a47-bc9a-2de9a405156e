import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export default function useDatafeedAction(info, options = {}) {
  const { 
    enableProfessionalOptimization = false 
  } = options
  
  // 暴露周期切换状态给外部组件
  let isResolutionChanging = ref(false)
  
  // 专业版历史数据加载状态管理
  const historicalDataStatus = enableProfessionalOptimization ? new Map() : null
  const HISTORICAL_DATA_TIMEOUT = 10000 // 10秒超时
  
  const dataCache = new Map()
  const requestCache = new Map()
  const activeRequests = new Map()
  const REQUEST_TIMEOUT = 30 * 1000
  
  const CACHE_DURATION = enableProfessionalOptimization 
    ? 25 * 60 * 1000
    : 10 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null
  const updateQueue = new Set<string>()
  const lastPriceUpdates = new Map<string, number>()
  const lastBarTimes = new Map<string, number>()
  

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }
  
  // 专业版历史数据状态管理函数
  const getHistoricalDataKey = (symbol: string, resolution: string) => {
    return `${symbol}-${resolutionMap[resolution]}`
  }
  
  const setHistoricalDataLoading = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return
    const key = getHistoricalDataKey(symbol, resolution)
    historicalDataStatus.set(key, { 
      loading: true, 
      ready: false, 
      timestamp: Date.now() 
    })
  }
  
  const setHistoricalDataReady = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return
    const key = getHistoricalDataKey(symbol, resolution)
    historicalDataStatus.set(key, { 
      loading: false, 
      ready: true, 
      timestamp: Date.now() 
    })
  }
  
  const isHistoricalDataReady = (symbol: string, resolution: string) => {
    if (!enableProfessionalOptimization || !historicalDataStatus) return true
    const key = getHistoricalDataKey(symbol, resolution)
    const status = historicalDataStatus.get(key)
    
    // 超时检查：如果加载超过10秒，认为已就绪（避免永久阻塞）
    if (status && status.loading && (Date.now() - status.timestamp > HISTORICAL_DATA_TIMEOUT)) {
      setHistoricalDataReady(symbol, resolution)
      return true
    }
    
    return status ? status.ready : false
  }

  const getConsistentTime = (serverTime: any, baseTime?: number): number => {
    if (serverTime && Number(serverTime) > 0) {
      return Number(serverTime)
    }
    return baseTime || Date.now()
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    if (firstDataRequest && resolution === '1M') {
      const hasValidCache = klineList.value.length > 0 && 
                           klineTicker.value.currentPeriod === '1M' &&
                           klineTicker.value.currentPair === symbolInfo.fullName
      
      if (hasValidCache) {
        preObj.value = klineList.value[0]
        onHistoryCallback(klineList.value, {noData: false})
        return true
      }
    }
    
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]
    key = `${symbolInfo.fullName}-${resolution}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    const currentResolution = resolutionMap[resolution]
    const isResolutionChange = firstDataRequest && interval.value && interval.value !== currentResolution
    
    if (isResolutionChange && !enableProfessionalOptimization) {
      const symbolPrefix = `${symbolInfo.fullName}-`
      const newResolutionPrefix = `${symbolInfo.fullName}-${currentResolution}-`
      
      const keysToDelete = []
      dataCache.forEach((value, key) => {
        if (key.startsWith(symbolPrefix) && !key.startsWith(newResolutionPrefix)) {
          keysToDelete.push(key)
        }
      })
      
      if (keysToDelete.length > 0) {
        keysToDelete.forEach(key => dataCache.delete(key))
      }
      
      const requestKeysToDelete = []
      requestCache.forEach((timestamp, key) => {
        if (key.startsWith(symbolPrefix) && !key.includes(`-${currentResolution}-`)) {
          requestKeysToDelete.push(key)
        }
      })
      
      if (requestKeysToDelete.length > 0) {
        requestKeysToDelete.forEach(key => requestCache.delete(key))
      }
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    if (!interval.value || isResolutionChange) {
      interval.value = currentResolution
    }
    
    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = (selective = false) => {
    if (selective) {
      dataCache.clear()
      lastCompleteBar.value = {}
      cleanupExpiredRequests()
    } else {
      cancelAllActiveRequests()
      dataCache.clear()
      lastCompleteBar.value = {}
      lastPriceUpdates.clear()
      lastBarTimes.clear()
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
      klineList.value = []
      klineTicker.value = {}
      
      // 专业版：清理历史数据状态
      if (enableProfessionalOptimization && historicalDataStatus) {
        historicalDataStatus.clear()
      }
    }
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      cleanupExpiredRequests()
    }
  }
  
  const cleanupExpiredRequests = () => {
    const now = Date.now()
    const expiredKeys = []
    
    requestCache.forEach((timestamp, key) => {
      if (now - timestamp > REQUEST_TIMEOUT) {
        expiredKeys.push(key)
      }
    })
    expiredKeys.forEach(key => {
      requestCache.delete(key)
      if (activeRequests.has(key)) {
        const controller = activeRequests.get(key)
        if (controller && !controller.signal.aborted) {
          controller.abort('Request timeout')
        }
        activeRequests.delete(key)
      }
    })
  }

  const cancelAllActiveRequests = () => {
    activeRequests.forEach((controller, key) => {
      if (controller && !controller.signal.aborted) {
        controller.abort('Component cleanup')
      }
    })
    activeRequests.clear()
    requestCache.clear()
  }

  setInterval(cleanupExpiredRequests, REQUEST_TIMEOUT / 2)

  const callStack = new Map<string, number>()
  const MAX_RECURSIVE_CALLS = 8
  
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const callKey = `${symbol}-${resolutionMap[resolution]}-${firstDataRequest}`
    const currentCalls = callStack.get(callKey) || 0
    if (currentCalls >= MAX_RECURSIVE_CALLS) {
      onErrorCallback('数据加载失败，请重试')
      return
    }
    callStack.set(callKey, currentCalls + 1)
    
    try {
      await fetchHistoricalDataInternal(symbol, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback)
    } finally {
      setTimeout(() => {
        callStack.delete(callKey)
      }, 1000)
    }
  }
  
  async function fetchHistoricalDataInternal(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    cleanupExpiredRequests()
    
    // 专业版：设置历史数据加载状态
    if (firstDataRequest) {
      setHistoricalDataLoading(symbol, resolution)
    }
    
    // 统一使用基于请求参数的缓存key，避免时间范围不连续问题
    const requestKey = `${symbol}-${resolutionMap[resolution]}-${from}-${to}-${countBack}`
    const cacheKey = enableProfessionalOptimization 
      ? `${symbol}-${resolutionMap[resolution]}-${Math.floor(from / 86400000)}-${firstDataRequest ? 'first' : 'hist'}`
      : `${symbol}-${resolutionMap[resolution]}-${firstDataRequest ? 'initial' : Math.floor(from / (24 * 60 * 60 * 1000))}`
    if (requestCache.has(requestKey)) {
      if (enableProfessionalOptimization) {
        const requestTime = requestCache.get(requestKey)
        if (Date.now() - requestTime >= 10000) {
          requestCache.delete(requestKey)
        } else {
          return
        }
      } else {
        return
      }
    }
    
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = resolution === '1M'
    
    const cacheTimeout = enableProfessionalOptimization
      ? (isMonthlyResolution ? 20 * 60 * 1000 : CACHE_DURATION)
      : (isMonthlyResolution ? 3 * 60 * 1000 : CACHE_DURATION)
    
    const shouldUseCache = cachedData && 
                          (Date.now() - cachedData.timestamp < cacheTimeout) && 
                          !forceRefresh.value &&
                          cachedData.data.length > 0
    
    if (shouldUseCache) {
      const noMoreData = cachedData.data.length < (firstDataRequest ? 30 : 10)
      if (forceRefresh.value) {
        forceRefresh.value = false
      }
      try {
        onHistoryCallback(cachedData.data, { noData: noMoreData })
        return
      } catch (error) {
        dataCache.delete(cacheKey)
      }
    }

    try {
      const controller = new AbortController()
      requestCache.set(requestKey, Date.now())
      activeRequests.set(requestKey, controller)
      
      const now = Date.now()
      const clamp = (value: number, min: number, max: number) => Math.min(Math.max(value, min), max)
      const requestLimit = firstDataRequest 
      ? clamp(countBack, 300, 1000) 
      : Math.min(countBack, 300)
      let beforeTime
      if (firstDataRequest) {
        beforeTime = now
      } else {
        beforeTime = preObj.value && (preObj.value as any).time ? (preObj.value as any).time : to
      }

      if (controller.signal.aborted) {
        return
      }

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: beforeTime,
        limit: requestLimit,
        origin:1,
      })
      
      if (controller.signal.aborted) {
        return
      }

      if (data) {
        // 🔍 调试日志：打印API返回的原始成交量数据
        console.log('🔍 [专业版-API数据] 原始成交量数据:', {
          symbol: formatSymbol(symbol),
          resolution: resolutionMap[resolution],
          firstItem: data.e[0],
          firstVolume: data.e[0] ? data.e[0][5] : 'N/A',
          lastItem: data.e[data.e.length - 1],
          lastVolume: data.e[data.e.length - 1] ? data.e[data.e.length - 1][5] : 'N/A',
          totalItems: data.e.length,
          requestParams: {
            before: beforeTime,
            limit: requestLimit,
            firstDataRequest
          }
        })

        let formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        // 🔍 调试日志：打印格式化后的成交量数据
        console.log('🔍 [专业版-格式化数据] 处理后成交量:', {
          firstFormatted: formattedData[0],
          lastFormatted: formattedData[formattedData.length - 1],
          传递给TradingView: '即将传递给TradingView'
        })
        
        // 严格的时间顺序控制：去重、排序、验证
        const uniqueData = new Map()
        formattedData.forEach(item => {
          if (item.time > 0) {
            uniqueData.set(item.time, item)
          }
        })
        
        // 严格按时间升序排列
        formattedData = Array.from(uniqueData.values()).sort((a, b) => a.time - b.time)
        
        // 严格的时间连续性验证
        const validatedData = []
        let lastTime = 0
        for (const item of formattedData) {
          // 确保时间严格递增，没有重复或倒序
          if (item.time > lastTime) {
            validatedData.push(item)
            lastTime = item.time
          }
        }
        formattedData = validatedData
        
        // 最终验证：确保数据按时间严格升序
        if (formattedData.length > 1) {
          for (let i = 1; i < formattedData.length; i++) {
            if (formattedData[i].time <= formattedData[i-1].time) {
              console.warn(`[TradingView] Time order violation detected at index ${i}, removing item`)
              formattedData.splice(i, 1)
              i-- // 调整索引
            }
          }
        }

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest && formattedData.length >= 10) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        const expectedMinData = firstDataRequest ? 50 : 10
        const noMoreData = formattedData.length === 0 || formattedData.length < expectedMinData
        
        // 专业版：成功加载后设置历史数据就绪状态
        if (firstDataRequest) {
          setHistoricalDataReady(symbol, resolution)
        }
        
        onHistoryCallback(formattedData, { noData: noMoreData })
      } else {
        // 专业版：加载失败时重置状态
        if (firstDataRequest) {
          setHistoricalDataReady(symbol, resolution)
        }
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      if (error.name === 'AbortError' || error.message?.includes('aborted') || error.message?.includes('timeout')) {
        return
      }
      // 专业版：错误时重置状态
      if (firstDataRequest) {
        setHistoricalDataReady(symbol, resolution)
      }
      onErrorCallback(error)
    } finally {
      try {
        requestCache.delete(requestKey)
        activeRequests.delete(requestKey)
      } catch (cleanupError) {
      }
    }
  }
  let lastCompleteBar = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    // 专业版：为月度数据提供ticker快速通道
    if (enableProfessionalOptimization) {
      if (interval.value !== '1M' || isResolutionChanging.value) {
        return false
      }
      
      const hasTickerUpdate = val1 && val1[pair.value] && val1[pair.value].last
      const hasCompleteKlineData = val2 && val2.currentPair === pair.value && val2.currentPeriod === '1M'
      
      // 如果有完整K线数据，执行严格检查
      if (hasCompleteKlineData && !isHistoricalDataReady(pair.value, '1M')) {
        return false
      }
      
      // 如果只有ticker数据，提供快速通道
      if (hasTickerUpdate && !hasCompleteKlineData) {
        const monthlyKey = `${pair.value}_#_1M`
        const last = val1[pair.value].last
        
        let monthlySubscription = null
        
        if (monthlySubscriptionCache.pair === pair.value &&
            monthlySubscriptionCache.key &&
            subMap[monthlySubscriptionCache.key]) {
          monthlySubscription = monthlySubscriptionCache.subscription
        } else if (subMap[monthlyKey]) {
          monthlySubscription = subMap[monthlyKey]
        }
        
        if (monthlySubscription && last) {
          const resultVal = {
            time: Date.now(),
            close: safeNumber(last),
            open: lastCompleteBar.value[monthlyKey]?.open || safeNumber(last),
            high: Math.max(lastCompleteBar.value[monthlyKey]?.high || safeNumber(last), safeNumber(last)),
            low: Math.min(lastCompleteBar.value[monthlyKey]?.low || safeNumber(last), safeNumber(last)),
            volume: lastCompleteBar.value[monthlyKey]?.volume || 0
          }
          
          const currentTime = resultVal.time
          const lastTime = lastBarTimes.get(monthlyKey) || 0
          if (currentTime > lastTime) {
            lastBarTimes.set(monthlyKey, currentTime)
            monthlySubscription.listen(resultVal)
          }
          return true
        }
        return false
      }
    }
    // 基本版：保持原有逻辑
    else if (interval.value !== '1M' || isResolutionChanging.value) {
      return false
    }

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const resultVal = {
        time: getConsistentTime(val2.time),
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      const monthlyStateKey = `${pair.value}_#_1M`
      lastCompleteBar.value[monthlyStateKey] = {
        time: resultVal.time,
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      const currentTime = resultVal.time || Date.now()
      const lastTime = lastBarTimes.get(monthlyStateKey) || 0
      // 严格时间验证：确保月度数据时间递增
      if (currentTime > lastTime) {
        lastBarTimes.set(monthlyStateKey, currentTime)
        monthlySubscription.listen(resultVal)
      }
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    // 专业版：检查历史数据加载状态和周期切换状态
    if (enableProfessionalOptimization) {
      if (isResolutionChanging.value) {
        return
      }
      
      // 扩大ticker快速通道：优先保证价格传递
      const hasTickerUpdate = val1 && val1[pair.value] && val1[pair.value].last
      const hasCompleteKlineData = val2 && val2.currentPair === pair.value && val2.currentPeriod
      
      // 使用实际数据中的周期，而不是全局的interval.value
      const actualPeriod = val2?.currentPeriod || interval.value
      const isHistoryReady = isHistoricalDataReady(pair.value, actualPeriod)
      
      // 优先处理ticker价格更新 - 扩大快速通道覆盖范围
      if (hasTickerUpdate) {
        const tickerData = val1[pair.value]
        // 关键修改：使用实际周期构建key
        const key = `${pair.value}_#_${actualPeriod}`
        
        // 如果有历史数据问题或者是纯ticker更新，都走快速通道
        if (!isHistoryReady || !hasCompleteKlineData) {
          
          if (subMap[key] && subMap[key].listen && tickerData.last) {
            // 🔍 调试日志：打印快速通道的成交量数据
            console.log('🔍 [专业版-快速通道] 成交量数据:', {
              symbol: pair.value,
              period: actualPeriod,
              hasCompleteKlineData,
              原始val2Volume: val2?.volume,
              处理后volume: hasCompleteKlineData ? safeNumber(val2.volume) : (lastCompleteBar.value[key]?.volume || 0),
              数据来源: hasCompleteKlineData ? 'klineTicker完整数据' : '缓存数据',
              isHistoryReady
            })

            const resultVal = {
              time: Date.now(),
              close: safeNumber(tickerData.last),
              open: hasCompleteKlineData ? safeNumber(val2.open) : (lastCompleteBar.value[key]?.open || safeNumber(tickerData.last)),
              high: hasCompleteKlineData ? Math.max(safeNumber(val2.high), safeNumber(tickerData.last)) :
                    Math.max(lastCompleteBar.value[key]?.high || safeNumber(tickerData.last), safeNumber(tickerData.last)),
              low: hasCompleteKlineData ? Math.min(safeNumber(val2.low), safeNumber(tickerData.last)) :
                   Math.min(lastCompleteBar.value[key]?.low || safeNumber(tickerData.last), safeNumber(tickerData.last)),
              volume: hasCompleteKlineData ? safeNumber(val2.volume) : (lastCompleteBar.value[key]?.volume || 0)
            }
            
            // 更新缓存（如果有完整数据的话）
            if (hasCompleteKlineData) {
              lastCompleteBar.value[key] = {
                time: resultVal.time,
                open: safeNumber(val2.open),
                high: safeNumber(val2.high),
                low: safeNumber(val2.low),
                volume: safeNumber(val2.volume)
              }
            }
            
            const currentTime = resultVal.time
            const lastTime = lastBarTimes.get(key) || 0
            if (currentTime > lastTime) {
              subMap[key].listen(resultVal)
              lastBarTimes.set(key, currentTime)
            }
          }
          return
        }
        
        // 如果有ticker、有完整数据、且历史数据就绪，继续正常流程
      }
    }
    // 基本版：只检查周期切换状态（保持原有逻辑）
    else if (isResolutionChanging.value) {
      return
    }
    
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    // 关键修改：优先使用数据中的实际周期，而不是全局的interval.value
    const actualPeriod = val2?.currentPeriod || interval.value
    const key = `${pair.value}_#_${actualPeriod}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          actualPeriod === val2.currentPeriod) {
        // 🔍 调试日志：打印WebSocket实时成交量数据
        console.log('🔍 [专业版-WebSocket数据] 实时成交量更新:', {
          symbol: pair.value,
          period: actualPeriod,
          原始val2Volume: val2.volume,
          处理后volume: safeNumber(val2.volume),
          ticker价格: last,
          完整val2数据: val2,
          时间戳: val2.time,
          数据来源: 'klineTicker WebSocket'
        })

        resultVal = {
          time: getConsistentTime(val2.time),
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          time: resultVal.time,
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(val2.time, baseBar.time),
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(null, baseBar.time),
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        resultVal = {
          time: getConsistentTime(null),
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      const currentUpdate = val1[pair.value] && val1[pair.value]._lastUpdate
      const lastUpdate = lastPriceUpdates.get(pair.value)
      const isPriceUpdate = currentUpdate && currentUpdate !== lastUpdate
      
      // 实时价格更新：确保时间严格递增
      if (isPriceUpdate) {
        lastPriceUpdates.set(pair.value, currentUpdate)
        if (subMap[key] && subMap[key].listen) {
          const currentTime = resultVal.time || Date.now()
          const lastTime = lastBarTimes.get(key) || 0
          // 严格时间验证：只有当新时间大于前一次时间才更新
          if (currentTime > lastTime) {
            subMap[key].listen(resultVal)
            lastBarTimes.set(key, currentTime)
          }
        }
      } else if (subMap[key] && subMap[key].listen) {
        // 对于非实时价格更新，仍然发送更新以保持K线图数据完整性
        const currentTime = resultVal.time || Date.now()
        const lastTime = lastBarTimes.get(key) || 0
        
        // 严格时间递增验证：确保新时间总是大于前一次时间
        if (currentTime > lastTime) {
          subMap[key].listen(resultVal)
          lastBarTimes.set(key, currentTime)
        }
      }
    }
  }, { 
    deep: true, 
    immediate: true,
    flush: 'sync'
  })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    // 彻底清理旧订阅状态
    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    // 清理月度订阅缓存
    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    // 完全清理所有状态，避免数据污染
    lastCompleteBar.value = {}
    lastBarTimes.clear()
    
    // 专业版：重置对应symbol的历史数据状态
    if (enableProfessionalOptimization && historicalDataStatus) {
      const key = getHistoricalDataKey(symbolInfo.fullName, resolution)
      historicalDataStatus.delete(key)
    }

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      actualResolution: resolutionMap[resolution], // 保存实际周期，用于数据匹配
      resetCallback: onResetCacheNeededCallback,
      listen: (newPriceData) => {
        try {
          // 在周期切换期间阻止实时数据推送
          if (isResolutionChanging && isResolutionChanging.value) {
            return
          }
          onRealtimeCallback(newPriceData)
        } catch (error) {
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  // 专业版统计报告函数
  const getCacheStats = () => {
    const total = cacheStats.hits + cacheStats.misses
    const hitRate = total > 0 ? Math.round(cacheStats.hits / total * 100) : 0
    return {
      hits: cacheStats.hits,
      misses: cacheStats.misses,
      apiCalls: cacheStats.apiCalls,
      hitRate: `${hitRate}%`,
      uptime: Math.round((Date.now() - cacheStats.lastReset) / 60000) + 'min'
    }
  }

  // 开发环境调试：将统计函数暴露到全局
  if (enableProfessionalOptimization && process.client && process.dev) {
    if (!window.__tradingViewStats) {
      window.__tradingViewStats = getCacheStats
      console.log('[TradingView Professional] Debug: window.__tradingViewStats() available')
    }
  }

  return {
    subMap, // 导出subMap供外部访问订阅状态
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        // timezone: 'Etc/UTC', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    setForceRefresh,
    cancelAllActiveRequests,
    getCacheStats: enableProfessionalOptimization ? getCacheStats : undefined,
    isResolutionChanging,
    triggerChartReset: (symbol, resolution) => {
      // TradingView官方推荐的重置机制
      const subscriptionKey = `${symbol}_#_${resolutionMap[resolution]}`
      const subscription = subMap[subscriptionKey]
      if (subscription && subscription.resetCallback) {
        subscription.resetCallback()
      }
    }
  }
}