<template>
  <div class="original-kline-wrap">
    <div
      v-if="isShowTool"
      class="line-tool"
      >
      <el-scrollbar
        class="line-tool-scroll-wrap"
        view-class="flex-box flex-column"
        tag="ul"
        >
        <el-tooltip
          v-for="item in drawlinedata"
          :key="item.label"
          :close-delay="0"
          :disabled="item.line"
          placement="right"
          :content="item.label"
          >
          <li
            v-if="item.line"
            class="tool-split-line"
          ></li>
          <template v-else>
            <li
              v-if="!item.line"
              :class="[
                {
                  'active': item.value === '' ? (!drawLine || drawLine === '') : (drawLine === item.value || item.active)
                }
              ]"
              class="line-tool-item mg-t4 ts-24"
              @click="drawlineChange(item.value)"
            >
              <MonoCross
                size="24"
                v-if="item.icon === 'icon-my-cross'"
              />
              <MonoFibonacci
                size="24"
                v-else-if="item.icon === 'icon-FibonacciRetracements'"
              />
              <MonoHorizonta
                size="24"
                v-else-if="item.icon === 'icon-my-horizontal-extended'"
              />
              <MonoParallel
                size="24"
                v-else-if="item.icon === 'icon-my-parallel-lines'"
              />
              <MonoPriceLine
                size="24"
                v-else-if="item.icon === 'icon-my-rice-line'"
              />
              <MonoRay
                size="24"
                v-else-if="item.icon === 'icon-my-ray'"
              />
              <MonoVerticalLine
                size="24"
                v-else-if="item.icon === 'icon-my-vertical-line'"
              />
              <MonoDelete
                size="24"
                v-else-if="item.icon === 'icon-my-delete'"
              />
            </li>
          </template>
        </el-tooltip>
      </el-scrollbar>
    </div>
    <div
      id="chart"
      class="charts-origin-box"
      :class="isShowTool ? 'offset' : ''"
    />
    <div
      v-if="isLoading"
      v-loading="isLoading"
      class="loadingBox"
    >
    </div>
  </div>
  <TechniCalInDicator
    v-if="isShowSetting"
    :originalTechnicalIndicatorSettings="originalTechnicalIndicatorSettings"
    :isShow="isShowSetting"
    @close="emit('closeSetting')"
    @updateOriginalTechnicalIndicatorSettings="updateSetting"
  />
</template>
<script lang="ts" setup>
import { ElScrollbar, ElTooltip } from 'element-plus'
import { init, dispose, registerLocale } from 'klinecharts'
import useOriginal, { baseTechnicalIndicatorMap } from '~/composables/useOriginal'
import MonoCross from '~/components/common/icon-svg/MonoCross.vue'
import MonoFibonacci from '~/components/common/icon-svg/MonoFibonacci.vue'
import MonoHorizonta from '~/components/common/icon-svg/MonoHorizonta.vue'
import MonoParallel from '~/components/common/icon-svg/MonoParallel.vue'
import MonoPriceLine from '~/components/common/icon-svg/MonoPriceLine.vue'
import MonoRay from '~/components/common/icon-svg/MonoRay.vue'
import MonoVerticalLine from '~/components/common/icon-svg/MonoVerticalLine.vue'
import TechniCalInDicator from './TechniCalInDicator.vue'
import { commonStore } from '~/stores/commonStore'
import { getKlinesApi } from '~/api/order'
import { nextTick } from 'vue'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const store = commonStore()
const { klineList, klineTicker, ticker, pairInfo, isPairDetail } = storeToRefs(store)
const props = defineProps({
  pair: {
    type: String,
    default: ''
  },
  isShowOriginalSetting: {
    type: Boolean,
    default: false
  },
  isShowDrawLine: {
    type: Boolean,
    default: true
  },
  resolution: {
    type: String,
    default: '15m'
  }
})
const isLoading = ref(false)
const emit = defineEmits(['closeSetting'])
let chart: any
const isShowTool = ref(false)
const isShowSetting = ref(false)
const drawlinedata = computed(() => {
  return [
    { label: t('十字线'), value: '', icon: 'icon-my-cross' },
    { line: true },
    { label: t('水平线'), value: 'horizontalStraightLine', icon: 'icon-my-horizontal-extended' },
    { label: t('垂直线'), value: 'verticalStraightLine', icon: 'icon-my-vertical-line' },
    { label: t('射线'), value: 'rayLine', icon: 'icon-my-ray' },
    // { label: t('平行射线'), value: 'horizontalRayLine', icon: 'icon-my-parallel-rays' },
    { label: t('平行直线'), value: 'parallelStraightLine', icon: 'icon-my-parallel-lines' },
    // { label: t('箭头'), value: 'JT', icon: 'icon-my-arrow' },
    { label: t('价格通道线'), value: 'priceLine', icon: 'icon-my-rice-line' },
    { label: t('斐波那契回调直线'), value: 'fibonacciLine', icon: 'icon-FibonacciRetracements' },
    { line: true },
    // { label: t('测量工具'), value: 'CL', icon: 'icon-Tools' },
    // { line: true },
    // { label: t('隐藏已画线'), value: 'yc', icon: 'icon-my-cover', active: !this.isShowLine },
    { label: t('清除画线'), value: 'del', icon: 'icon-my-delete' }
  ]
})
const drawLine = ref('')
watch(() => props.isShowOriginalSetting, (settingVal) => {
  isShowSetting.value = settingVal
})
watch(() => isShowTool.value, (val) => {
  nextTick(() => {
    chart && chart.resize()
  })
})
watch(() => props.isShowDrawLine, (val) => {
  isShowTool.value = val
}, { immediate: true })
watch(() => colorMode.preference, (val) => {
  chart.setStyles(val)
  if (val === 'light') {
    document.getElementById('chart').style.backgroundColor = '#ffffff'
  } else if (val === 'dark') {
    document.getElementById('chart').style.backgroundColor = '#1b1b1f'
  }
})

watch(() => locale.value, (lang) => {
  chart.setLocale(lang)
})
const drawlineChange = (shapeName: string) => {
  drawLine.value = shapeName
  if (shapeName === 'del') {
    chart.removeOverlay()
    drawLine.value = ''
  } else {
    chart.createOverlay({
      name: shapeName,
      points: [],
      styles: {
        text: {
          offset: [-2, 0]
        },
        line: {
          size: 2
        }
      },
      onRightClick() {
        return true
      },
      onDrawEnd({ overlay: { points } }: any) {
        drawLine.value = ''
      }
    })
  }
}
const originalTechnicalIndicatorSettings = ref({
  checkedIndicators: { // 基础版k线当前选中的技术指标
    main: ['MA'],
    sub: ['VOL']
  },
  technicalIndicatorSettings: baseTechnicalIndicatorMap
})
// 设置主指标参数和样式
const mainSettingFunc = () => {
  // 主指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: [],
      circles: []
    }
    const basicSettings = settings[name].setting

    if (['MA', 'EMA', 'SMMA'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'BOLL') {
      calcParams = [basicSettings.cycle.value, basicSettings.deviation.value]
      styles.lines = [{
        size: 1,
        color: basicSettings.BOLL.color
      }, {
        size: 1,
        color: basicSettings.UB.color
      }, {
        size: 1,
        color: basicSettings.DB.color
      }]
    } else if (name === 'SAR') {
      calcParams = [
        basicSettings.start.value,
        basicSettings.step.value,
        basicSettings.max.value
      ]
      styles.circles = [{
        upColor: basicSettings.SAR.color,
        downColor: basicSettings.SAR.color,
        noChangeColor: basicSettings.SAR.color
      }]
    }
    chart.overrideIndicator({
      name,
      visible: true,
      calcParams,
      styles
    })
  })
}
// 设置副指标参数和样式
const subSettingFunc = () => {
  // 副指标
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name: any) => {
    const settings: any = (originalTechnicalIndicatorSettings.value.technicalIndicatorSettings as any)
    if (!settings[name]) {
      return false
    }
    let calcParams: any = []
    const styles: any = {
      lines: []
    }
    const basicSettings = settings[name].setting
    if (['VOL', 'RSI'].includes(name)) {
      for (const key in basicSettings) {
        const setting: any = basicSettings[key]
        setting.show && calcParams.push(setting.value)
        setting.show && styles.lines.push({
          size: 1,
          color: setting.color
        })
      }
    } else if (name === 'MACD') {
      calcParams = [basicSettings.MACD1.value, basicSettings.MACD2.value, basicSettings.MACD3.value]
      styles.line.colors = [basicSettings.DIF.color, basicSettings.DEA.color]
    } else if (name === 'KDJ') {
      calcParams = [basicSettings.KDJ1.value, basicSettings.KDJ2.value, basicSettings.KDJ3.value]
      styles.line.colors = [basicSettings.K.color, basicSettings.D.color, basicSettings.J.color]
    } else if (name === 'OBV') {
      calcParams = [basicSettings.MAOBV.value]
      styles.line.colors = [basicSettings.OBV.color, basicSettings.MAOBV.color]
    } else if (name === 'CCI') {
      calcParams = [basicSettings.CCI.value]
      styles.line.colors = [basicSettings.CCI.color]
    } else if (name === 'WR') {
      calcParams = [basicSettings.WR.value]
      styles.line.colors = [basicSettings.WR.color]
    } else if (name === 'DMI') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.PDI.color, basicSettings.MDI.color, basicSettings.ADX.color, basicSettings.ADXR.color]
    } else if (name === 'MTM') {
      calcParams = [basicSettings.N.value, basicSettings.MM.value]
      styles.line.colors = [basicSettings.MTM.color, basicSettings.MAMTM.color]
    } else if (name === 'EMV') {
      calcParams = [basicSettings.EMV1.value, basicSettings.EMV2.value]
      styles.line.colors = [basicSettings.EMV.color, basicSettings.MAEMV.color]
    }
    chart.overrideIndicator({
      name,
      calcParams,
      styles
    })
  })
}
const paneMap: any = {}
const initTechnicalIndicator = () => {
  const nowPane: any = []
  originalTechnicalIndicatorSettings.value.checkedIndicators.main.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true, { id: 'candle_pane' })
      paneMap[name] = paneId
    }
  })
  originalTechnicalIndicatorSettings.value.checkedIndicators.sub.forEach((name) => {
    nowPane.push(name)
    if (!paneMap[name]) {
      const paneId = chart.createIndicator(name, true)
      paneMap[name] = paneId
    }
  })
  Object.keys(paneMap).forEach(name => {
    if (!nowPane.includes(name)) {
      chart.removeIndicator(paneMap[name], name)
      delete paneMap[name]
    }
  })
}
const isReady = ref(false)
const lastPriceUpdate = ref(0)
const lastUpdateTime = ref(0)
const updateChart = val => {
  if (val.length > 0 && JSON.stringify(pairInfo.value[props.pair]) !== '{}') {
    isLoading.value = false
    chart && chart.setPrecision({
      price: (pairInfo.value[props.pair] || {}).price_scale,
      volume: 2
    })
  }
  if (val.length > 0) {
    const timer = setTimeout(() => {
      const latestData = val[val.length - 1]
      const newTime = latestData.time || Date.now()
      
      // 确保时间递增，更新最新的K线数据
      if (newTime > lastUpdateTime.value) {
        lastUpdateTime.value = newTime
        chart && chart.updateData(latestData)
        
        // 同步最新价格到ticker（如果ticker价格不同步）
        if (ticker.value[props.pair] && latestData.close) {
          const tickerPrice = Number(ticker.value[props.pair].last)
          const klinePrice = Number(latestData.close)
          
          // 如果K线价格与ticker价格差异较大，强制同步ticker价格
          if (Math.abs(tickerPrice - klinePrice) > 0.001) {
            const syncData = {
              ...latestData,
              close: Math.abs(tickerPrice),
              time: Date.now()
            }
            lastUpdateTime.value = syncData.time
            chart && chart.updateData(syncData)
            console.log('[BasicKline] Syncing ticker price to K-line:', tickerPrice)
          }
        }
      }
      clearTimeout(timer)
    }, 0)
  }
}

watch(klineList, (val) => {
  chart && chart.applyNewData(val, true)
  updateChart(val)
})

watch(() => ticker.value[props.pair], (currentTicker, previousTicker) => {
  // 强化的ticker价格实时更新：确保ticker价格变化立即反映到K线图
  if (currentTicker && chart) {
    const hasNewUpdate = currentTicker._lastUpdate && 
                        currentTicker._lastUpdate !== lastPriceUpdate.value
    const hasPriceChange = !previousTicker || 
                          previousTicker.last !== currentTicker.last
    
    // 只要有价格变化或者明确的更新标记，就执行更新
    if ((hasNewUpdate || hasPriceChange) && currentTicker.last) {
      lastPriceUpdate.value = currentTicker._lastUpdate || Date.now()
      
      // 强制使用当前时间戳，确保更新不被时间验证阻塞
      const forceUpdateTime = Date.now()
      
      const priceData = {
        close: Math.abs(Number(currentTicker.last)),
        time: forceUpdateTime
      }
      
      // 强制更新：无论时间戳如何，ticker价格变化都要更新到K线图
      lastUpdateTime.value = forceUpdateTime
      chart.updateData(priceData)
      
      console.log('[BasicKline] Ticker price updated:', currentTicker.last, 'at', forceUpdateTime)
    }
  }
}, { immediate: false, deep: true })

watch([ticker, klineTicker], ([val1,val2]) => {
  const last = (val1[props.pair] || {}).last
  if (last) {
    const currentTickerPrice = Number(last) < 0 ? -Number(last) : Number(last)
    
    // 检查是否有价格变化，用于强制更新判断
    const lastKlinePrice = chart && chart.getDataByIndex ? 
      chart.getDataByIndex(-1)?.close : null
    const hasPriceChange = !lastKlinePrice || 
      Math.abs(lastKlinePrice - currentTickerPrice) > 0.001
    
    // 🔍 调试日志：基本版WebSocket实时数据
    console.log('🔍 [基本版-WebSocket数据] 实时成交量更新:', {
      symbol: props.pair,
      原始val2Volume: val2.volume,
      处理后volume: Number(val2.volume) < 0 ? -Number(val2.volume) : Number(val2.volume),
      ticker价格: currentTickerPrice,
      完整val2数据: val2,
      时间戳: val2.time,
      数据来源: 'klineTicker WebSocket'
    })

    const resultVal = {
      close: currentTickerPrice,
      currentPair: val2.currentPair,
      currentPeriod: val2.currentPeriod,
      high: Number(val2.high) < 0 ? -Number(val2.high) : Number(val2.high),
      low: Number(val2.low) < 0 ? -Number(val2.low) : Number(val2.low),
      open: Number(val2.open) < 0 ? -Number(val2.open) : Number(val2.open),
      time: Number(val2.time),
      timestamp: Number(val2.timestamp),
      undefined: Number(val2.undefined),
      volume: Number(val2.volume) < 0 ? -Number(val2.volume) : Number(val2.volume)
    }
    
    const klineTime = Number(resultVal.time) || Date.now()
    const shouldUpdate = klineTime > lastUpdateTime.value || hasPriceChange
    
    // 放宽时间验证：允许价格变化时强制更新，即使时间戳较旧
    if (shouldUpdate) {
      // 如果是价格变化但时间戳较旧，使用当前时间确保时间递增
      const updateTime = hasPriceChange && klineTime <= lastUpdateTime.value ? 
        lastUpdateTime.value + 1 : klineTime
      
      lastUpdateTime.value = updateTime
      resultVal.time = updateTime

      // 🔍 调试日志：基本版最终传递给klinecharts的数据
      console.log('🔍 [基本版-最终传递] 发送给klinecharts的成交量:', {
        symbol: props.pair,
        最终volume: resultVal.volume,
        完整数据: resultVal,
        数据来源: hasPriceChange ? '价格变化强制更新' : '正常更新',
        时间戳: updateTime
      })

      chart && chart.updateData(resultVal)
      
      if (hasPriceChange) {
        console.log('[BasicKline] Price change detected, forced update:', currentTickerPrice, 'at', updateTime)
      }
    }
  }
}, {
  deep: true
})
const updateSetting = (data) => {
  originalTechnicalIndicatorSettings.value = data
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
}
const transChartData = (obj) => {
  const keys = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
  const finalItem = {
    time: Number(obj[0])
  }
  obj.forEach((v, i) => {
    finalItem[keys[i]] = Number(v) < 0 ? -Number(v) : Number(v)
  })

  // 🔍 调试日志：基本版API数据处理
  if (keys[5] === 'volume') {
    console.log('🔍 [基本版-API数据] transChartData处理:', {
      原始数据: obj,
      原始volume: obj[5],
      处理后volume: finalItem.volume,
      时间戳: finalItem.time
    })
  }

  return finalItem
}
const request = async (end?: number, callback?: any, retryCount = 3) => {
  // 基本版1日周期禁用：不调用API接口
  if (props.resolution === '1d') {
    return []
  }
  
  try {
    const { data } = await getKlinesApi({
      symbol: props.pair,
      market: props.pair.includes('_SWAP') ? 'lpc' : 'spot',
      time_frame: props.resolution,
      before: end,
      limit: 300
    });
    
    if (data?.e) {
      // 🔍 调试日志：基本版API返回的原始数据
      console.log('🔍 [基本版-API原始数据] getKlinesApi返回:', {
        symbol: props.pair,
        resolution: props.resolution,
        firstItem: data.e[0],
        firstVolume: data.e[0] ? data.e[0][5] : 'N/A',
        lastItem: data.e[data.e.length - 1],
        lastVolume: data.e[data.e.length - 1] ? data.e[data.e.length - 1][5] : 'N/A',
        totalItems: data.e.length
      })

      const klineData = data.e.map(item => transChartData(item));

      // 🔍 调试日志：基本版处理后的数据
      console.log('🔍 [基本版-处理后数据] 传递给klinecharts:', {
        firstProcessed: klineData[0],
        lastProcessed: klineData[klineData.length - 1],
        传递给klinecharts: '即将传递给klinecharts'
      })

      return klineData;
    } else {
      return [];
    }
  } catch (error) {
    if (error.response?.status === 504 && retryCount > 0) {
      // 如果是504错误且还有重试次数，等待一段时间后重试
      const delay = (4 - retryCount) * 1000; // 重试延迟时间递增：1s, 2s, 3s
      await new Promise(resolve => setTimeout(resolve, delay));
      return request(end, callback, retryCount - 1);
    }
    return [];
  }
}
const chartInit = () => {
  const subscriptNumbers = {
    0: '₀',
    1: '₁',
    2: '₂',
    3: '₃',
    4: '₄',
    5: '₅',
    6: '₆',
    7: '₇',
    8: '₈',
    9: '₉',
    10: '₁₀',
    11: '₁₁',
    12: '₁₂',
    13: '₁₃',
    14: '₁₄',
    15: '₁₅',
    16: '₁₆',
    17: '₁₇',
    18: '₁₈',
    19: '₁₉',
    20: '₂₀',
    21: '₂₁',
    22: '₂₂',
    23: '₂₃',
    24: '₂₄',
    25: '₂₅',
    26: '₂₆',
    27: '₂₇',
    28: '₂₈',
    29: '₂₉',
    30: '₃₀',
    31: '₂₁',
    32: '₃₂',
    33: '₃₃'
  }
  chart = init('chart', {
    thousandsSeparator: '',
    decimalFoldThreshold: 8,
    loadMore: {
      isLoadMore: true, // 启用加载更多
    }
  }) as any
  chart.setLoadMoreDataCallback(async ({ type, data, callback }) => {
    if (type === 'forward') {
      // 基本版1日周期：禁用历史数据加载，仅使用WebSocket数据
      if (props.resolution === '1d') {
        callback([], false);
        return;
      }
      
      const end = data ? data.timestamp : Date.now();
      const klineData = await request(end);
      callback(klineData, klineData.length === 1000);
    } else {
      callback([], false);
    }
  });
  chart && chart.applyNewData(klineList.value, klineList.value.length === 1000)
  chart.setDecimalFold({
    format: value => {
      let vl = `${value}`;
      const [integer, decimalPart] = vl.split('.');
      const trimmedDecimalPart = decimalPart ? decimalPart.replace(/0+$/, '') : '';
      vl = integer + (trimmedDecimalPart ? '.' + trimmedDecimalPart : '');
      const reg = new RegExp('\\.0{8,}[1-9][0-9]*$');
      if (reg.test(vl)) {
        const result = vl.split('.');
        const lastIndex = result.length - 1;
        const v = result[lastIndex];
        const match = /0*/.exec(v);
        if (match) {
          const count = match[0].length;
          result[lastIndex] = v.replace(/0*/, `0${subscriptNumbers[count]}`);
          return result.join('.')
        }
      }
      return vl;
    }
  })
  chart.setStyles(useOriginal(colorMode.preference, 'green-up'))
  chart.setLocale(locale.value)
  initTechnicalIndicator()
  mainSettingFunc()
  subSettingFunc()
  registerLocale('zh', {
    time: '时间：',
    open: '开：',
    high: '高：',
    low: '低：',
    close: '收：',
    volume: '成交量：'
  })
  registerLocale('en', {
    time: 'Time：',
    open: 'Open：',
    high: 'High：',
    low: 'Low：',
    close: 'Close：',
    volume: 'Volume：'
  })
  registerLocale('ja', {
    time: '時間：',
    open: '始値：',
    high: '高値：',
    low: '安値：',
    close: '終値：',
    volume: '出来高：'
  })
  registerLocale('ko', {
    time: '시간：',
    open: '시가：',
    high: '고가：',
    low: '저가：',
    close: '종가：',
    volume: '거래량：'
  })
  registerLocale('zh-Hant', {
    time: '時間：',
    open: '開盤：',
    high: '最高：',
    low: '最低：',
    close: '收盤：',
    volume: '成交量：'
  })
  isReady.value = true
}
onMounted(() => {
  isLoading.value = true
  window.addEventListener(
    'resize',
    () => {
      setTimeout(() => {
        chart && chart.resize()
      }, 300)
    }
  )
  chartInit()

  // 增强的数据检查和加载机制：确保基本版能够正确显示数据
  const checkAndLoadData = () => {
    const hasValidData = klineList.value.length > 0
    const isValidPair = props.pair && props.pair.length > 0
    const isValidResolution = props.resolution && props.resolution.length > 0
    
    // 检查是否需要重新加载数据
    if (!hasValidData && isValidPair && isValidResolution) {
      console.log('[BasicKline] Reloading data for:', props.pair, props.resolution)
      
      nextTick(() => {
        // 通过store重新获取K线数据
        store.getKlineSocket(props.pair, props.resolution)
      })
    }
    
    // 特别处理1日周期：确保WebSocket订阅正常
    if (props.resolution === '1d' && (!hasValidData || !klineTicker.value.currentPair)) {
      console.log('[BasicKline] Special handling for 1d period, ensuring WebSocket subscription')
      nextTick(() => {
        store.getKlineSocket(props.pair, props.resolution)
      })
    }
  }
  
  // 执行数据检查
  checkAndLoadData()
  
  // 设置延迟检查机制，防止网络延迟导致的数据缺失
  setTimeout(() => {
    if (klineList.value.length === 0) {
      console.log('[BasicKline] Secondary data check - reloading if still empty')
      checkAndLoadData()
    }
  }, 1000)
})
onUnmounted(() => {
})
</script>
<style lang="scss" scoped>
.original-kline-wrap {
  width: 100%;
  height: calc(100% - 42px);
  position: relative;
  overflow: hidden; // 添加这行
  .line-tool {
    position: relative;
    height: 100%;
    width: 52px;
    z-index: 1;

    .line-tool-scroll-wrap {
      position: relative;
      height: 100%;
      width: 52px;
    }

    .line-tool-item {
      width: 34px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      @include color(tc-secondary);

      &:hover {
        @include color(theme);
        @include bg-color(bg-quaternary);
      }

      &.active {
        @include color(theme);
      }

      &:not(:first-of-type) {
        margin-top: 4px;
      }
    }

    .tool-split-line {
      height: 1px;
      width: 100%;
      margin: 4px 0;
      @include bg-color(border);
    }
  }

  .loadingBox {
    width: 100%;
    height: 100%;
    position: absolute;
    @include bg-color(bg-primary);
    z-index: 999;
    left: 0;
    top: 0;
  }
}

.charts-origin-box {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  transform: translateZ(0); // 添加硬件加速
  &.offset {
    left: 52px;
    width: calc(100% - 52px);
  }
}
@include mb {
  .original-kline-wrap {
    width: 100%;
    height: calc(100% - 44px);
  }
}
</style>